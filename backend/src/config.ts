import dotenv from 'dotenv';
import path from 'path';

const env = process.env.NODE_ENV || 'development';
const envFile = `.env.${env}`;

dotenv.config({ 
  path: path.resolve(process.cwd(), envFile),
  override: true 
});

export const config = {
  env,
  isDevelopment: env === 'development',
  isProduction: env === 'production',
  port: process.env.PORT ? parseInt(process.env.PORT, 10) : 3000,
  database: {
    url: process.env.DATABASE_URL!,
    ensureSafeEnvironment() {
      if (this.url.includes('_prod') && config.isDevelopment) {
        console.error('🚨 CRITICAL ERROR: Attempting to access production database in development mode!');
        console.error('Please check your environment configuration.');
        process.exit(1);
      }
      
      if (config.isDevelopment && !this.url.includes('_dev')) {
        console.warn('⚠️  Warning: Development database name should include "_dev" suffix');
      }
    }
  },
  email: {
    user: process.env.GMAIL_USER!,
    password: process.env.GOOGLE_APP_PASSWORD!,
  },
  apis: {
    googleApiKey: process.env.GOOGLE_API_KEY!,
    seventeenTrackKey: process.env.SEVENTEEN_TRACK_API_KEY!,
    parcelsAppKey: process.env.PARCELS_APP_API_KEY!,
    coinGeckoUrl: process.env.COINGECKO_URL!,
  }
};

config.database.ensureSafeEnvironment();
{"name": "peptide-order-portal", "version": "0.1.0", "description": "Peptide order management system with crypto payment tracking", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"dev": "./scripts/dev-start.sh", "dev:stop": "./scripts/dev-stop.sh", "dev:api": "npm run dev --workspace=backend", "dev:ui": "npm run dev --workspace=frontend", "prod:api": "npm run dev:prod --workspace=backend", "prod:ui": "npm run dev:prod --workspace=frontend", "build:api": "npm run build --workspace=backend", "build:ui": "npm run build --workspace=frontend", "start:api": "npm run start --workspace=backend", "start:api:dev": "npm run start:dev --workspace=backend", "db:push": "npm run db:push --workspace=backend", "db:push:prod": "npm run db:push:prod --workspace=backend", "db:seed": "npm run db:seed --workspace=backend", "db:seed:prod": "npm run db:seed:prod --workspace=backend"}, "keywords": [], "author": "", "license": "MIT", "dependencies": {"@types/react-datepicker": "^6.2.0", "playwright": "^1.54.1"}}
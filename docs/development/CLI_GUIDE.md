# Peptide Order Portal CLI Guide

## Quick Start

Run the CLI from the project root:
```bash
python3 peptide-cli.py
# or
./peptide-cli.py
```

## Features

### 🎯 Main Menu
The CLI starts with a color-coded environment indicator:
- **Green** = Development (safe)
- **Red** = Production (careful!)

### 🚀 1. Start Servers
- **Auto Port Management**: Automatically kills processes on ports 3000 & 5173 before starting
- **Options**:
  - Start both servers (API + UI)
  - Start API only (port 3000)
  - Start UI only (port 5173)
  - Stop all servers

### 🗄️  2. Database Operations
- **Push Schema**: Update database structure
- **Seed Database**: Add product data
- **Delete Orders**: Clear all orders (dev only)
- **Show Info**: View connection details

### 🔄 3. Switch Environment
- Toggle between development and production
- Production switches require confirmation
- Affects all subsequent operations

### 📊 4. Show Status
- Current environment settings
- Server status (running/stopped)
- Database configuration
- Port usage

## Safety Features

1. **Production Warnings**: Red color coding and confirmations
2. **Port Management**: Auto-kills conflicting processes
3. **Environment Protection**: Some operations disabled in production
4. **Double Confirmations**: Critical operations require typing "PRODUCTION"

## Common Workflows

### Daily Development
```
1. Run ./peptide-cli.py
2. Choose "1" (Start Servers)
3. Choose "1" (Start Both)
4. Work on your project
5. When done, choose "4" (Stop All)
```

### Database Updates
```
1. Make sure you're in development (green)
2. Choose "2" (Database Operations)
3. Choose "1" (Push Schema)
4. Choose "2" (Seed Database)
```

### Switch to Production
```
1. Choose "3" (Switch Environment)
2. Confirm the switch
3. Now all operations affect production
4. Switch back when done!
```

## Tips

- Press Ctrl+C anytime to exit
- The CLI remembers your environment choice
- Servers run in background - you can close the CLI
- Check status anytime with option "4"
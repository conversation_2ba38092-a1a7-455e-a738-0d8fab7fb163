# Environment Setup & Safety Guide

## Database Isolation Strategy

This project uses strict environment separation to protect production data.

### Database Names
- **Development**: `peptide_portal_dev`
- **Production**: `peptide_portal_prod`

### Environment Files
```
backend/
├── .env.development    # Development configuration
├── .env.production     # Production configuration  
└── .env.example        # Template for new environments
```

## Safety Features

### 1. Automatic Database Validation
The application includes safety checks that:
- Prevent access to production database when in development mode
- Exit with error if `_prod` database is accessed in dev environment
- Warn if database name doesn't follow naming convention

### 2. Environment-Specific Commands

#### Development (Default)
```bash
# From project root
npm run dev:api          # Start dev API server
npm run dev:ui           # Start dev frontend
npm run db:push          # Push schema to dev database
npm run db:seed          # Seed dev database

# Backend specific
cd backend && npm run dev
```

#### Production
```bash
# From project root
npm run prod:api         # Start prod API server (careful!)
npm run db:push:prod     # Push schema to prod database
npm run db:seed:prod     # Seed prod database

# Backend specific
cd backend && npm run dev:prod
```

## Workflow Best Practices

### 1. Daily Development
- Always work on `dev` branch
- Use development database (`peptide_portal_dev`)
- Test thoroughly before considering production

### 2. Production Deployment
```bash
# Only when ready for production
gh checkout main
gh merge dev --no-ff
npm run build:api
npm run start:api        # Runs with NODE_ENV=production

# Always return to dev
gh checkout dev
```

### 3. Database Operations
```bash
# Create production database (one time)
createdb peptide_portal_prod

# Push schema to production (careful!)
npm run db:push:prod

# Seed production pricing (if needed)
npm run db:seed:prod
```

## Environment Check Script

Run before commits:
```bash
./scripts/check-environment.sh
```

This script will:
- Warn if production database is referenced in dev mode
- Prevent production mode on dev branch
- Ensure safe environment configuration

## Troubleshooting

### "Production database accessed in development mode"
1. Check your `.env.development` file
2. Ensure DATABASE_URL ends with `_dev`
3. Restart the server

### "Cannot find .env.development"
1. Copy from example: `cp backend/.env.example backend/.env.development`
2. Update with your actual values
3. Ensure database name is `peptide_portal_dev`

## Important Reminders

1. **NEVER** commit `.env.*` files (except `.env.example`)
2. **ALWAYS** double-check database name before operations
3. **USE** `npm run db:delete-orders` only in development
4. **TEST** thoroughly in dev before touching production

## Quick Reference

| Environment | Database Name | Branch | NODE_ENV |
|------------|--------------|--------|----------|
| Development | peptide_portal_dev | dev | development |
| Production | peptide_portal_prod | main | production |
# VS Code Development Setup

This document explains how to set up VS Code with convenient status bar buttons for managing the development servers.

## 🎯 Quick Setup

### Step 1: Install Required Extension
1. Open VS Code
2. Install the **Action Buttons** extension by `se<PERSON><PERSON><PERSON><PERSON>`
   - Go to Extensions (Ctrl+Shift+X)
   - Search for "Action Buttons"
   - Install "VsCode Action Buttons" by se<PERSON><PERSON><PERSON>e

### Step 2: Open Workspace
- Open the `pop.code-workspace` file in VS Code
- VS Code will automatically suggest installing the Action Buttons extension if not already installed

## 🚀 Status Bar Buttons

Once set up, you'll see these buttons in the VS Code status bar (bottom):

### 🚀 Start Servers (Green)
- **Command**: `npm run dev`
- **Function**: Starts both frontend and backend development servers using PM2
- **Ports**: Frontend on 5173, Backend on 3000
- **Features**: Hot reload enabled for both services

### 🛑 Stop Servers (Red)
- **Command**: `npm run dev:stop`
- **Function**: Stops both development servers
- **Use**: When you're done coding or need to free up ports

### 🔄 Restart Servers (Yellow)
- **Command**: `npm run dev:stop && npm run dev`
- **Function**: Stops and then starts both servers
- **Use**: When you need a fresh restart (e.g., after environment changes)

### 📊 Server Status (Blue)
- **Command**: `pm2 list`
- **Function**: Shows current PM2 process status
- **Use**: Check if servers are running and their status

## 🎨 Button Features

- **Color-coded**: Easy visual identification with semantic colors
- **Theme Compatible**: Colors optimized for both Solarized Light and Dracula themes
- **High Contrast**: WCAG-compliant contrast ratios for accessibility
- **Tooltips**: Hover for detailed descriptions
- **Single Instance**: Prevents multiple executions
- **Terminal Integration**: Commands run in VS Code terminal
- **Workspace Scoped**: Only active when this project is open

## 🔧 Alternative Setup Methods

### Method 1: Workspace File (Recommended)
- Configuration is in `pop.code-workspace`
- Automatically loads when opening the workspace
- Shared across team members

### Method 2: Local Settings
- Configuration is in `.vscode/settings.json`
- Works when opening the folder directly
- Local to your machine

## 🛠️ Customization

To modify the buttons, edit either:
- `pop.code-workspace` (workspace method)
- `.vscode/settings.json` (folder method)

### Button Configuration Options:
```json
{
  "name": "Button Text",
  "color": "#hexcolor",
  "command": "shell command",
  "tooltip": "Description",
  "singleInstance": true,
  "cwd": "${workspaceFolder}"
}
```

### Available Colors (Theme Compatible):
- Green: `#22c55e` (success/start) - Works in both light and dark themes
- Red: `#ef4444` (danger/stop) - High contrast in Solarized Light and Dracula
- Orange: `#f97316` (warning/restart) - Better visibility than yellow in both themes
- Blue: `#3b82f6` (info/status) - Balanced blue for optimal readability

## 🚨 Troubleshooting

### Buttons Not Showing
1. Ensure Action Buttons extension is installed and enabled
2. Reload VS Code window (Ctrl+Shift+P → "Developer: Reload Window")
3. Check if workspace is properly opened

### Commands Not Working
1. Ensure you're in the correct directory
2. Check that npm scripts exist in package.json
3. PM2 will be automatically installed globally when you first run `npm run dev`

### Extension Not Found
- Extension ID: `seunlanlege.action-buttons`
- Marketplace: [Action Buttons](https://marketplace.visualstudio.com/items?itemName=seunlanlege.action-buttons)

## 📝 Development Workflow

1. **Start of Day**: Click 🚀 Start Servers
2. **Code**: Hot reload handles changes automatically
3. **Check Status**: Click 📊 Server Status if needed
4. **Restart**: Click 🔄 Restart Servers if issues occur
5. **End of Day**: Click 🛑 Stop Servers (or leave running)

## 🎯 Benefits

- **One-Click Operations**: No need to remember commands
- **Visual Feedback**: Color-coded status
- **Persistent Servers**: PM2 keeps servers running
- **Hot Reload**: Automatic updates on file changes
- **Team Consistency**: Shared configuration across developers

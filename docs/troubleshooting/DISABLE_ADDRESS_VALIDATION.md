# How to Disable Address Validation

## Quick Fix (Immediate)

To disable address validation immediately, open your browser's Dev<PERSON>per Console (F12 or right-click → Inspect → Console) while on the Peptide Order Portal and run:

```javascript
localStorage.setItem('skipAddressValidation', 'true');
```

Then refresh the page. Address validation will be disabled for all future orders.

## Via Settings Page

1. Click on "Settings" in the navigation menu
2. Toggle "Skip Address Validation" to ON
3. The setting will be saved automatically

## To Re-enable

Run this in the console:
```javascript
localStorage.setItem('skipAddressValidation', 'false');
```

Or toggle it back OFF in the Settings page.
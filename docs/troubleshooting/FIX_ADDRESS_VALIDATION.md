# Fix Google Address Validation API Error

## The Problem
The Google Address Validation API is returning a 403 PERMISSION_DENIED error with the reason "API_KEY_SERVICE_BLOCKED". This means the Address Validation API is not enabled for your Google Cloud project.

## Solution

### Step 1: Enable the Address Validation API
1. Go to [Google Cloud Console APIs Library](https://console.cloud.google.com/apis/library)
2. Search for "Address Validation API"
3. Click on "Address Validation API" in the results
4. Click the "ENABLE" button
5. Wait for the API to be enabled (this may take a few seconds)

### Step 2: Verify API Key Permissions (if needed)
1. Go to [Google Cloud Console Credentials](https://console.cloud.google.com/apis/credentials)
2. Click on your API key (the one in your .env file)
3. Under "API restrictions", make sure either:
   - "Don't restrict key" is selected (for development), OR
   - "Restrict key" is selected and "Address Validation API" is in the list of selected APIs

### Step 3: Test the Fix
Run the test script to verify it's working:
```bash
cd peptide-order-portal/backend
npx tsx src/scripts/test-google-api.ts
```

You should see a successful response instead of the 403 error.

## Why This Happened
- The Google Places API (used for address autocomplete) was enabled and working
- But the Address Validation API is a separate service that needs to be explicitly enabled
- Both APIs use the same API key, but each needs to be enabled individually

## Note on Billing
The Address Validation API may require billing to be enabled on your Google Cloud project. If you see a billing-related error after enabling the API, you'll need to:
1. Enable billing on your Google Cloud project
2. Note that Google provides $200 in free credits monthly for Maps APIs
3. Address Validation API costs $0.005 per request (first 100K requests per month)

## Alternative: Keep Validation Disabled
If you prefer not to enable billing or use the Address Validation API, you can keep address validation disabled via the Settings page. The order system will work perfectly fine without it.
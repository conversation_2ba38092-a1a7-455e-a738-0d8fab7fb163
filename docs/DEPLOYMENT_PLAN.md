# Deployment Plan: Dev → Main → Render

## Overview
This document outlines the complete deployment plan for migrating the dev branch and database to main branch and production database, then deploying to Render.com with a custom domain (pop.nobledragons.com).

## Pre-Deployment Checklist

### 1. Backup Current State
- Create database backup: 
  ```bash
  /opt/homebrew/Cellar/postgresql@15/15.13/bin/pg_dump peptide_portal_dev > backup_$(date +%Y%m%d_%H%M%S).sql
  ```
- Create git backup branches:
  ```bash
  git checkout -b dev-backup-$(date +%Y%m%d_%H%M%S)
  git checkout dev
  ```

### 2. Environment Verification
- [ ] Ensure all dev changes are committed and pushed
- [ ] Verify dev database has latest order data
- [ ] Check Render CLI authentication: `render whoami`

## Phase 1: Database Migration (Dev → Main)

### 3. Backup Production Database
```bash
/opt/homebrew/Cellar/postgresql@15/15.13/bin/pg_dump peptide_portal_prod > backup_prod_$(date +%Y%m%d_%H%M%S).sql
```

### 4. Export Dev Database
```bash
/opt/homebrew/Cellar/postgresql@15/15.13/bin/pg_dump peptide_portal_dev > dev_data_export.sql
```

### 5. Update Production Database
⚠️ **WARNING: This will delete all production data**
```bash
# Drop and recreate production database
/opt/homebrew/Cellar/postgresql@15/15.13/bin/psql -c "DROP DATABASE IF EXISTS peptide_portal_prod;"
/opt/homebrew/Cellar/postgresql@15/15.13/bin/psql -c "CREATE DATABASE peptide_portal_prod;"

# Import dev data to production database
/opt/homebrew/Cellar/postgresql@15/15.13/bin/psql peptide_portal_prod < dev_data_export.sql
```

## Phase 2: Code Migration (Dev → Main)

### 6. Update Main Branch
```bash
git checkout main
git pull origin main
git merge dev --no-ff -m "Merge dev into main for production deployment"
```

### 7. Resolve Any Conflicts
- If conflicts arise, resolve them maintaining dev's versions
- Test locally after merge

### 8. Push Updated Main
```bash
git push origin main
```

## Phase 3: Render Deployment Setup

### 9. Create render.yaml Configuration
Create a `render.yaml` file in the project root:

```yaml
services:
  - type: web
    name: peptide-portal-backend
    runtime: node
    repo: https://github.com/iambaseddev/peptide-order-portal
    branch: main
    region: singapore
    rootDir: backend
    buildCommand: npm install && npm run build
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3000
      - key: DATABASE_URL
        fromDatabase:
          name: peptide-portal-db
          property: connectionString
      - key: GMAIL_USER
        sync: false
      - key: GOOGLE_APP_PASSWORD
        sync: false
      - key: GOOGLE_API_KEY
        sync: false
      - key: SEVENTEEN_TRACK_API_KEY
        sync: false
      - key: PARCELS_APP_API_KEY
        sync: false
      - key: COINGECKO_URL
        value: https://api.coingecko.com/api/v3

  - type: web
    name: peptide-portal-frontend
    runtime: static
    repo: https://github.com/iambaseddev/peptide-order-portal
    branch: main
    region: singapore
    rootDir: frontend
    buildCommand: npm install && npm run build
    staticPublishPath: ./dist
    envVars:
      - key: VITE_API_BASE_URL
        value: https://peptide-portal-backend.onrender.com/api
    headers:
      - path: /*
        name: X-Frame-Options
        value: SAMEORIGIN

databases:
  - name: peptide-portal-db
    region: singapore
    plan: starter
    postgresMajorVersion: 16
```

### 10. Deploy Using Render CLI
```bash
# Create services from render.yaml
render create

# Or deploy manually:
render services create --name peptide-portal-backend --repo https://github.com/iambaseddev/peptide-order-portal --branch main --region singapore
render databases create --name peptide-portal-db --postgres-version 16 --region singapore
```

### 11. Configure Environment Variables on Render
Via Render Dashboard or CLI, set the following secrets:
- `GMAIL_USER` - Your Gmail address
- `GOOGLE_APP_PASSWORD` - Gmail app password
- `GOOGLE_API_KEY` - Google Places API key
- `SEVENTEEN_TRACK_API_KEY` - 17track API key
- `PARCELS_APP_API_KEY` - Parcels App API key

Update frontend `VITE_API_BASE_URL` to backend URL after deployment.

### 12. Database Setup on Render
After the database is created:
```bash
# Get connection string from Render dashboard
# Import your data
/opt/homebrew/Cellar/postgresql@15/15.13/bin/psql [RENDER_DATABASE_URL] < dev_data_export.sql
```

## Phase 4: Domain Configuration

### 13. Configure Custom Domain on Render
In Render Dashboard, add custom domains:
- Backend: `api.pop.nobledragons.com`
- Frontend: `pop.nobledragons.com`

### 14. Update Cloudflare DNS
Add CNAME records:
```
pop.nobledragons.com → [frontend-service].onrender.com
api.pop.nobledragons.com → [backend-service].onrender.com
```
Enable Cloudflare proxy (orange cloud)

### 15. Update Frontend API URL
Update `VITE_API_BASE_URL` in Render frontend environment to:
```
https://api.pop.nobledragons.com/api
```
Trigger rebuild on Render

## Phase 5: Post-Deployment Verification

### 16. Test Deployment
- [ ] Verify backend API: `curl https://api.pop.nobledragons.com/api/pricing`
- [ ] Check frontend at: https://pop.nobledragons.com
- [ ] Test order creation and management
- [ ] Verify email functionality
- [ ] Check tracking integrations

### 17. Monitor Logs
```bash
render logs --service peptide-portal-backend --tail
render logs --service peptide-portal-frontend --tail
```

## Rollback Plan

### Database Rollback
```bash
/opt/homebrew/Cellar/postgresql@15/15.13/bin/psql -c "DROP DATABASE IF EXISTS peptide_portal_prod;"
/opt/homebrew/Cellar/postgresql@15/15.13/bin/psql -c "CREATE DATABASE peptide_portal_prod;"
/opt/homebrew/Cellar/postgresql@15/15.13/bin/psql peptide_portal_prod < backup_prod_[timestamp].sql
```

### Code Rollback
```bash
git checkout main
git revert [merge-commit-hash]
git push origin main
```

### Render Rollback
- Use Render's rollback feature in dashboard
- Or redeploy previous commit

## Important Notes

- **Database Migration**: This will REPLACE all production data with dev data
- **API Keys**: Ensure all secrets are properly configured on Render before deployment
- **CORS**: Backend should already be configured to accept requests from the frontend domain
- **SSL**: Render provides free SSL certificates, Cloudflare will handle SSL for custom domain
- **Build Time**: Initial deployment may take 10-15 minutes
- **Database Connection**: Render will inject DATABASE_URL automatically
- **Frontend API URL**: Currently set to `http://localhost:3000/api` in `.env.local`, will need to be updated for production

## Current Configuration
- **Databases**: 
  - Dev: `peptide_portal_dev`
  - Prod: `peptide_portal_prod`
- **GitHub Repository**: https://github.com/iambaseddev/peptide-order-portal
- **Region**: Singapore
- **Custom Domain**: pop.nobledragons.com (hosted on Cloudflare)
- **PostgreSQL Path**: `/opt/homebrew/Cellar/postgresql@15/15.13/bin/psql`
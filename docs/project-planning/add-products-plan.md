# Product Management Implementation Plan

## Overview
Add a complete product management interface to allow admin users to create, edit, delete, and bulk import products directly from the frontend, replacing the current JSON file + seed script approach.

## Task List

### 1. Backend API Development
- **Create CRUD endpoints for products:**
  - `POST /api/pricing` - Create single product
  - `PUT /api/pricing/:code` - Update product
  - `DELETE /api/pricing/:code` - Delete product
  - `POST /api/pricing/bulk` - Bulk import products (CSV/JSON)
  - `GET /api/pricing/export` - Export current products

- **Add validation:**
  - Unique product codes
  - Valid price ranges
  - Required fields validation
  - Prevent deletion of products used in orders

### 2. Frontend Product Management UI
- **Create new page: `/products` (ProductManagement.tsx)**
  - Product list with search/filter
  - Add/Edit/Delete buttons
  - Bulk import section
  
- **Product List Features:**
  - Display all products grouped by name
  - Show code, dose, buying price, selling price, profit margin
  - Sort by product name, dose, price
  - Search by product name or code
  - Edit inline or modal
  - Delete with confirmation

### 3. Add/Edit Product Modal
- **Form fields:**
  - Product name (dropdown with option to add new)
  - Dose (text input with unit)
  - Product code (auto-generate or manual)
  - Buying price (USD)
  - Selling price (USD)
  - Show calculated profit margin
  
- **Validation:**
  - Ensure selling price > buying price
  - Unique product code
  - Required fields

### 4. Bulk Import Feature
- **Import options:**
  - CSV file upload
  - JSON file upload
  - Copy/paste text area
  
- **Import workflow:**
  - Upload/paste data
  - Preview imported products
  - Validate data (highlight errors)
  - Option to replace all or append
  - Show import summary

### 5. UI Integration
- **Navigation:**
  - Add "Products" link to main navigation
  - Add product count to dashboard

## Implementation Phases

### Phase 1: Backend API (Task 1)
- Create CRUD endpoints
- Add validation logic
- Test with API tools

### Phase 2: Basic Product Management (Tasks 2-3)
- Create product list page
- Implement add/edit/delete
- Basic search/filter

### Phase 3: Bulk Import (Task 4)
- Add import UI
- Implement CSV/JSON parsing
- Add validation & preview

### Phase 4: Polish & Integration (Task 5)
- Add confirmations
- Update navigation
- Final testing

## Technical Details

### Database Changes
- Add indexes for performance
- Add `created_at` timestamp to pricing table

### API Structure
```typescript
// Single product
POST /api/pricing
{
  "product": "Tirzepatide",
  "dose": "5 mg",
  "code": "TR5",
  "buyingPrice": 48,
  "sellingPrice": 89
}

// Bulk import
POST /api/pricing/bulk
{
  "mode": "append" | "replace",
  "products": [...]
}
```

### CSV Format
```csv
Product,Dose,Code,Buying Price,Selling Price
Tirzepatide,5 mg,TR5,48,89
Tirzepatide,10 mg,TR10,70,119
```

## Benefits
1. No more manual JSON editing
2. Immediate updates without server restart
3. Bulk import for efficiency
4. Better error handling
5. User-friendly interface
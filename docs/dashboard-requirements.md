# Dashboard Redesign Implementation

## Context
I need to redesign the dashboard at `/Users/<USER>/alexwu/pop/frontend/src/pages/Dashboard.tsx`. The current dashboard shows all-time metrics and includes a recent orders table that needs to be removed.

## Requirements

### 1. Remove Recent Orders Section
- Delete the entire "Recent Orders" table component from the dashboard
- Remove any related API calls or data fetching for recent orders
- Clean up unused imports and types

### 2. Add Profit Date Range Filter
Create a date range selector with three options:
- **Today** (default selection)
- **Last 7 Days**
- **Last 30 Days**

Implementation details:
- Place the selector prominently at the top of the dashboard
- Update all profit metrics (Total Profit USD/INR, Profit by Product) based on selection
- Modify the API endpoint to accept date range parameters

### 3. Revenue & Profit Trend Chart (30 Days)
Create a combination line chart showing both revenue and profit trends:
- **X-axis**: Date (daily intervals for last 30 days)
- **Y-axis**: Amount in USD
- **Data series**: 
  - Revenue line (total order value)
  - Profit line (revenue minus costs)
- Include legend, grid lines, and hover tooltips with formatted values
- Use different colors for revenue (e.g., blue) and profit (e.g., green)

### 4. Daily Orders Chart (30 Days)
Create a bar chart showing order volume:
- **X-axis**: Date (daily intervals for last 30 days)
- **Y-axis**: Number of orders
- Display order count for each day
- Include hover tooltips showing exact count and date

### 5. Shipping Status Overview
Create a new section displaying shipping status distribution:
- Show count and percentage for each status:
  - **Delivered** (green)
  - **Info Received** (blue)
  - **In Transit** (orange)
- Use visual indicators (e.g., progress cards or donut chart)
- Pull data from order shipping statuses

## Technical Implementation

### Backend Updates (dashboard.service.ts):
- Modify getDashboardMetrics to accept optional date range parameters
- Add date filtering logic using placedAt field
- Add new methods:
  - getDailyRevenueProfit(days: number): Returns daily revenue/profit for chart
  - getDailyOrderCount(days: number): Returns daily order counts
  - getShippingStatusDistribution(): Returns status counts

### Frontend Updates:
- Use existing Recharts library for all charts
- Implement date range state management
- Create reusable chart components
- Use Tailwind CSS for consistent styling
- Add loading states for each data section

## Deliverables
1. Updated Dashboard.tsx without recent orders table
2. Three new chart components with proper TypeScript types
3. Updated dashboard service with date filtering
4. Responsive design that works on mobile devices
5. Smooth loading states and error handling
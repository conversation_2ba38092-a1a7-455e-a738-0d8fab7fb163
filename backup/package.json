{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "NODE_ENV=development vite", "dev:prod": "NODE_ENV=production vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "test:demo": "playwright test --grep \"Address validation workflow demonstration\""}, "dependencies": {"@types/react-router-dom": "^5.3.3", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-datepicker": "^8.4.0", "react-day-picker": "^9.8.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.30.1", "@playwright/test": "^1.54.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.3"}}
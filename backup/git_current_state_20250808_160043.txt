a3aa7b4 chore: organize project structure - move docs to docs/ folder and archive screenshot script
b88ac55 chore: archive logs, backups, demos, tests, temp files and ignore archive/
ac5a745 Add 'lifetime' date range and UI polish; add backend scripts
85118ad Update AGENTS.md with comprehensive development environment documentation
e2dfd70 Fix order number sorting on orders page
79e5ce2 Add automatic time-based dark mode switching
1392980 Merge branch 'dev'
1c86751 Fix Google Places API key in production environment
5f6f2e0 Prevent database backups from being committed to remote repository and organize backup management with dedicated scripts and directory structure.
1caf3b0 Add tracking modal to Order History page for bulk tracking entry

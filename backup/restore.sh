#!/bin/bash

# Peptide Order Portal - Complete Restoration Script
# Created: August 8, 2025 - 16:01 UTC
# This script restores the system to the exact state after our session

echo "🔄 Starting Peptide Order Portal Restoration..."
echo "Timestamp: $(date)"
echo ""

# Check if we're in the right directory
if [ ! -f "backup/database_full_backup_20250808_160020.sql" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    echo "   Expected to find: backup/database_full_backup_20250808_160020.sql"
    exit 1
fi

echo "📋 Restoration Steps:"
echo "1. Database restoration"
echo "2. Git repository reset"
echo "3. Environment setup"
echo "4. Dependency installation"
echo ""

read -p "Continue with restoration? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Restoration cancelled"
    exit 1
fi

echo ""
echo "🗄️  Step 1: Restoring Database..."

# Drop and recreate database
echo "   - Dropping existing database..."
psql -U sagar -d postgres -c "DROP DATABASE IF EXISTS peptide_portal_dev;" > /dev/null 2>&1

echo "   - Creating fresh database..."
psql -U sagar -d postgres -c "CREATE DATABASE peptide_portal_dev;" > /dev/null 2>&1

echo "   - Restoring data from backup..."
psql -U sagar -d peptide_portal_dev < backup/database_full_backup_20250808_160020.sql > /dev/null 2>&1

# Verify restoration
ORDER_COUNT=$(psql -U sagar -d peptide_portal_dev -t -c "SELECT COUNT(*) FROM orders;" 2>/dev/null | xargs)
if [ "$ORDER_COUNT" = "89" ]; then
    echo "   ✅ Database restored successfully (89 orders)"
else
    echo "   ❌ Database restoration failed (expected 89 orders, got $ORDER_COUNT)"
    exit 1
fi

echo ""
echo "📦 Step 2: Resetting Git Repository..."

# Reset to the clean commit
echo "   - Resetting to commit a3aa7b47..."
git checkout dev > /dev/null 2>&1
git reset --hard a3aa7b47accefb6d58c313010fa783e433cfec4a > /dev/null 2>&1

# Verify git state
CURRENT_COMMIT=$(git rev-parse --short HEAD)
if [ "$CURRENT_COMMIT" = "a3aa7b4" ]; then
    echo "   ✅ Git repository reset successfully"
else
    echo "   ❌ Git reset failed (expected a3aa7b4, got $CURRENT_COMMIT)"
    exit 1
fi

echo ""
echo "⚙️  Step 3: Setting up Environment..."

# Copy environment files
echo "   - Copying environment configuration..."
cp backup/.env backend/ 2>/dev/null || echo "   - No .env file to copy"
cp backup/.env.development backend/ 2>/dev/null || echo "   - No .env.development file to copy"

echo ""
echo "📦 Step 4: Installing Dependencies..."

# Install root dependencies
echo "   - Installing root dependencies..."
npm install > /dev/null 2>&1

# Install backend dependencies
echo "   - Installing backend dependencies..."
cd backend && npm install > /dev/null 2>&1 && cd ..

# Install frontend dependencies
echo "   - Installing frontend dependencies..."
cd frontend && npm install > /dev/null 2>&1 && cd ..

echo ""
echo "🎉 RESTORATION COMPLETE!"
echo ""
echo "📊 System Status:"
echo "   Database: peptide_portal_dev (89 orders, 126 order_items)"
echo "   Git Branch: dev (commit a3aa7b4)"
echo "   Environment: Development (no authentication)"
echo ""
echo "🚀 To start the application:"
echo "   Backend:  cd backend && npm run dev"
echo "   Frontend: cd frontend && npm run dev"
echo ""
echo "🌐 Access URLs:"
echo "   Frontend: http://localhost:5173"
echo "   Backend:  http://localhost:3000"
echo ""
echo "✅ Restoration completed at: $(date)"

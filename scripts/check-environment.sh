#!/bin/bash

# Check if we're in development environment and accidentally using production database
if [ "$NODE_ENV" = "development" ] || [ -z "$NODE_ENV" ]; then
  # Check if any .env file contains production database
  if grep -q "_prod" backend/.env* 2>/dev/null; then
    echo "⚠️  WARNING: Production database reference found in environment files!"
    echo "Please ensure you're not accidentally using production database in development."
    read -p "Continue anyway? (y/N) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
      exit 1
    fi
  fi
fi

# Check if we're on dev branch and NODE_ENV is production
CURRENT_BRANCH=$(git branch --show-current)
if [ "$CURRENT_BRANCH" = "dev" ] && [ "$NODE_ENV" = "production" ]; then
  echo "⚠️  WARNING: You're on dev branch but NODE_ENV is set to production!"
  echo "This could lead to accidental production database changes."
  exit 1
fi

echo "✅ Environment check passed"